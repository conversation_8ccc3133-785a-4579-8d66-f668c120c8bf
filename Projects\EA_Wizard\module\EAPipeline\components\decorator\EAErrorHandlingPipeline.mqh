#property strict

#include "../EAPipelineBase.mqh"
#include "../EAErrorHandler.mqh"

//+------------------------------------------------------------------+
//| EAErrorHandlingPipeline 類 - 錯誤處理裝飾者模式實現的 EAPipelineBase |
//+------------------------------------------------------------------+
class EAErrorHandlingPipeline : public EAPipelineBase
{
private:
    EAErrorHandler* m_error_handler;  // 錯誤處理器

public:
    // 構造函數
    EAErrorHandlingPipeline(Pipeline* pipeline, string type = "EAErrorHandlingPipeline", EAErrorHandler* error_handler = NULL)
        : EAPipelineBase(pipeline, type),
          m_error_handler(error_handler ? error_handler : EAErrorHandler::GetInstance())
    {
    }

    EAErrorHandlingPipeline(EAPipelineBase* pipeline, string type = "EAErrorHandlingPipeline", EAErrorHandler* error_handler = NULL)
        : EAPipelineBase(pipeline, type),
          m_error_handler(error_handler ? error_handler : EAErrorHandler::GetInstance())
    {
        EAPipelineBase::SetPipeline(pipeline.GetPipeline());
    }

    // 析構函數
    virtual ~EAErrorHandlingPipeline()
    {
        // 不需要刪除 m_error_handler，因為它是單例
    }



    // 獲取流水線執行結果並處理錯誤
    virtual PipelineResult* GetResult() override
    {
        PipelineResult* result = EAPipelineBase::GetResult();

        // 檢查結果並處理錯誤
        if(result != NULL && !result.IsSuccess())
        {
            string error_msg = "EAErrorHandlingPipeline 獲取結果失敗: " + GetName() + ", 原因: " + result.GetMessage();
            m_error_handler.HandleError(error_msg);
        }

        return result;
    }
};
